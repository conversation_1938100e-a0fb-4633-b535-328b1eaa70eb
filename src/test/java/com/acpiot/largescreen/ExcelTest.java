package com.acpiot.largescreen;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
public class ExcelTest {

    public static final int MAX_COLUMN_INDEX = 7;

    public static void appendColumn(String filePath, String newColumnName) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = WorkbookFactory.create(fis)) {

            // 新列样式
            CellStyle newStyle = workbook.createCellStyle();
            newStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
            newStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            newStyle.setAlignment(HorizontalAlignment.CENTER);
            newStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            newStyle.setBorderBottom(BorderStyle.THIN);
            newStyle.setBorderTop(BorderStyle.THIN);
            newStyle.setBorderLeft(BorderStyle.THIN);
            newStyle.setBorderRight(BorderStyle.THIN);

            for (Sheet sheet : workbook) {
                // 获取最后一行的行号
                int lastRowNum = sheet.getLastRowNum();

                // 获取最大列数
                final int maxColumnIndex = MAX_COLUMN_INDEX;

                // 设置列宽
                sheet.setColumnWidth(maxColumnIndex + 1, 25 * 128);

                // for (int i = 0; i <= lastRowNum; i++) {
                //     Row row = sheet.getRow(i);
                //     if (row != null) {
                //         maxColumnIndex = Math.max(maxColumnIndex, row.getLastCellNum());
                //     }
                // }

                // 倍数
                Integer multiple = null;

                // 剩余库存
                String remainingStock = "";

                // 在每行末尾添加新列
                for (int i = 0; i <= lastRowNum; i++) {
                    Row row = sheet.getRow(i);
                    if (row == null) {
                        row = sheet.createRow(i);
                    }

                    if (i == 3) {
                        // 这里是数量单元格
                        Cell cell = row.getCell(maxColumnIndex);
                        if (cell.getCellType() == CellType.NUMERIC) {
                            multiple = (int) cell.getNumericCellValue();
                        } else if (cell.getCellType() == CellType.STRING) {
                            String cellVal = cell.getStringCellValue();
                            if (StrUtil.isNotBlank(cellVal)) {
                                multiple = Integer.parseInt(cellVal);
                            }
                        }

                        Assert.notNull(multiple, "第 {} 行 {} 列数量数据错误，请检查。", i + 1, maxColumnIndex);

                        log.info("multiple: {}", multiple);
                    } else if (i >= 5 && i < lastRowNum) {
                        // 这行开始处理数据

                        // 料号
                        String materialCode = row.getCell(1).getStringCellValue();
                        if (StrUtil.isNotBlank(materialCode)) {
                            // 用量
                            Cell cell = row.getCell(maxColumnIndex - 1);
                            Integer usage = null;
                            if (cell != null) {
                                if (cell.getCellType() == CellType.NUMERIC) {
                                    usage = (int) cell.getNumericCellValue();
                                } else if (cell.getCellType() == CellType.STRING) {
                                    String cellVal = cell.getStringCellValue();
                                    if (StrUtil.isNotBlank(cellVal)) {
                                        usage = Integer.parseInt(cellVal);
                                    }
                                }
                            }

                            Assert.notNull(usage, "第 {} 行 {} 列用量数据错误，请检查。", i + 1, maxColumnIndex);

                            // 实际用量
                            assert usage != null && multiple != null;
                            int actualUsage = usage * multiple;
                            remainingStock = String.valueOf(actualUsage);
                        } else {
                            remainingStock = "料号列为空，请检查";
                        }
                    }

                    Cell newCell = row.createCell(maxColumnIndex + 1); // 插入新列
                    if (i == 0) {
                        newCell.setCellValue(newColumnName); // 设置标题
                    } else if (i < 5 || i == lastRowNum) {
                        newCell.setCellValue(""); // 默认空值
                    } else {
                        newCell.setCellValue(remainingStock);
                    }

                    // 保留原行样式（可选）
                    // if (i > 0 && row.getCell(0) != null) {
                    //     CellStyle originalStyle = row.getCell(0).getCellStyle();
                    //     newCell.setCellStyle(originalStyle);
                    // }

                    newCell.setCellStyle(newStyle);
                }
            }

            // 写回原文件
            try (FileOutputStream fos = new FileOutputStream("D:\\Users\\moxin\\Desktop\\生产BOM_new.xls")) {
                workbook.write(fos);
            }
        }
    }

    public static void main(String[] args) throws IOException {
        String filePath = "D:\\Users\\moxin\\Desktop\\生产BOM.xls"; // 替换为你的文件路径
        String newColumnName = "库存减用量"; // 新列名
        appendColumn(filePath, newColumnName);
        log.info("Done。");
    }

}

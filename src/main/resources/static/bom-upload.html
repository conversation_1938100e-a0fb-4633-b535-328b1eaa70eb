<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物料BOM文件上传</title>
    <link href="https://fonts.loli.net/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="js/libs/vue-toast-notification/theme-bootstrap.min.css">
    <link rel="stylesheet" href="css/bom-upload.css">
</head>
<body>
<div id="app" class="container">
    <div class="header">
        <a href="index.html" style="color:#007bff;text-decoration:underline;font-weight:500;font-size:1.05rem;">返回主页</a>
        <h1 style="flex:1;text-align:center;margin:0;">物料BOM文件管理</h1>
        <div style="width:80px;"></div> <!-- 占位元素保持居中 -->
    </div>
    
    <div class="content">
        <!-- 文件上传区域 -->
        <div class="panel upload-panel">
            <div class="panel-header">
                <span>文件上传</span>
            </div>
            <div class="panel-content">
                <div class="upload-area" @click="triggerFileInput" @dragover.prevent @drop.prevent="handleDrop">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                        <p>点击选择文件或拖拽文件到此处</p>
                        <p class="upload-hint">支持 .xlsx, .xls 格式的Excel文件</p>
                    </div>
                    <input 
                        ref="fileInput" 
                        type="file" 
                        accept=".xlsx,.xls" 
                        @change="handleFileSelect" 
                        style="display: none;"
                    >
                </div>
                
                <!-- 上传进度 -->
                <div v-if="uploading" class="upload-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" :style="{width: uploadProgress + '%'}"></div>
                    </div>
                    <div class="progress-text">上传中... {{ uploadProgress }}%</div>
                </div>
            </div>
        </div>
        
        <!-- 文件列表区域 -->
        <div class="panel file-list-panel">
            <div class="panel-header">
                <span>已上传文件</span>
                <button class="refresh-btn" @click="loadFileList" title="刷新列表">
                    <span class="refresh-icon">↻</span>
                </button>
            </div>
            <div class="panel-content">
                <div v-if="fileList.length === 0" class="empty-state">
                    <div class="empty-icon">📄</div>
                    <p>暂无文件</p>
                </div>
                
                <div v-else class="file-list">
                    <div 
                        v-for="(file, index) in fileList" 
                        :key="index" 
                        class="file-item"
                    >
                        <div class="file-info">
                            <div class="file-icon">📊</div>
                            <div class="file-details">
                                <div class="file-name" :title="file">{{ file }}</div>
                                <div class="file-meta">Excel文件</div>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button 
                                class="action-btn download-btn" 
                                @click="downloadFile(file)"
                                title="下载文件"
                            >
                                ⬇
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="js/libs/vue.min.js"></script>
<script src="js/libs/vue-toast-notification/index.min.js"></script>
<script src="js/libs/axios.js"></script>
<script src="js/bom-upload.js"></script>
</body>
</html>

// HTTP客户端配置
const http = axios.create({
    baseURL: '',
    timeout: 30000 // 文件上传需要更长的超时时间
});

// 创建全局加载元素
const loadingElement = document.createElement('div');
loadingElement.id = 'global-loading';
loadingElement.innerHTML = '<div class="loading-spinner"></div>';
document.body.appendChild(loadingElement);

// 请求拦截器
http.interceptors.request.use(
    config => {
        // 文件上传时不显示全局loading，使用进度条
        if (config.url !== '/material/upload') {
            document.getElementById('global-loading').style.display = 'flex';
        }
        return config;
    },
    error => {
        document.getElementById('global-loading').style.display = 'none';
        Vue.$toast.error('请求发送失败');
        return Promise.reject(error);
    }
);

// 响应拦截器
http.interceptors.response.use(
    response => {
        document.getElementById('global-loading').style.display = 'none';
        return response.data;
    },
    error => {
        document.getElementById('global-loading').style.display = 'none';

        let message = '操作失败';
        if (error.response) {
            const status = error.response.status;
            const data = error.response.data;

            if (status === 400) {
                message = data.message || '请求参数错误';
            } else if (status === 401) {
                message = '未授权访问';
            } else if (status === 403) {
                message = '禁止访问';
            } else if (status === 404) {
                message = '请求的资源不存在';
            } else if (status === 500) {
                message = data.message || '服务器内部错误';
            } else {
                message = data.message || `请求失败 (${status})`;
            }
        } else if (error.request) {
            message = '网络连接失败，请检查网络';
        } else {
            message = error.message || '未知错误';
        }

        Vue.$toast.error(message);
        return Promise.reject(error);
    }
);

// 初始化Vue插件
Vue.use(VueToast);

Vue.prototype.$http = http;

// Vue应用实例
new Vue({
    el: '#app',
    data: {
        fileList: [],
        uploading: false,
        uploadProgress: 0,
        showDeleteConfirm: false,
        fileToDelete: null
    },
    mounted() {
        this.loadFileList();
    },
    methods: {
        // 触发文件选择
        triggerFileInput() {
            this.$refs.fileInput.click();
        },

        // 处理文件选择
        handleFileSelect(event) {
            const files = event.target.files;
            if (files.length > 0) {
                this.uploadFile(files[0]);
            }
        },

        // 处理拖拽上传
        handleDrop(event) {
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                // 检查文件类型
                if (this.isValidFileType(file)) {
                    this.uploadFile(file);
                } else {
                    this.$toast.error('请选择Excel文件（.xlsx 或 .xls 格式）');
                }
            }
        },

        // 验证文件类型
        isValidFileType(file) {
            const validTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
                'application/vnd.ms-excel' // .xls
            ];
            return validTypes.includes(file.type) ||
                   file.name.toLowerCase().endsWith('.xlsx') ||
                   file.name.toLowerCase().endsWith('.xls');
        },

        // 上传文件
        async uploadFile(file) {
            if (!this.isValidFileType(file)) {
                this.$toast.error('请选择Excel文件（.xlsx 或 .xls 格式）');
                return;
            }

            // 检查文件大小（限制为10MB）
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (file.size > maxSize) {
                this.$toast.error('文件大小不能超过10MB');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            this.uploading = true;
            this.uploadProgress = 0;

            try {
                await this.$http.post('/material/upload', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    },
                    onUploadProgress: (progressEvent) => {
                        this.uploadProgress = Math.round(
                            (progressEvent.loaded * 100) / progressEvent.total
                        );
                    }
                });

                this.$toast.success('文件上传成功');
                this.loadFileList(); // 刷新文件列表

                // 清空文件输入
                this.$refs.fileInput.value = '';

            } catch (error) {
                console.error('文件上传失败:', error);
            } finally {
                this.uploading = false;
                this.uploadProgress = 0;
            }
        },

        // 加载文件列表
        async loadFileList() {
            try {
                const files = await this.$http.get('/material/list');
                this.fileList = files || [];
            } catch (error) {
                console.error('加载文件列表失败:', error);
                this.fileList = [];
            }
        },

        // 下载文件
        downloadFile(fileName) {
            const downloadUrl = `/download/material/${encodeURIComponent(fileName)}`;

            // 创建临时链接进行下载
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = fileName;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.$toast.success('开始下载文件');
        }
    }
});

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品库存监控大屏</title>
    <link href="https://fonts.loli.net/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="js/libs/vue-toast-notification/theme-bootstrap.min.css">
    <link rel="stylesheet" href="css/index.css">
</head>
<body>
<div id="app" class="container">
    <div class="header" style="display:flex;align-items:center;justify-content:space-between;">
        <div style="display:flex;gap:15px;">
            <a href="task-list.html" target="_blank" style="color:#007bff;text-decoration:underline;font-weight:500;font-size:1.05rem;">任务清单</a>
            <a href="bom-upload.html" target="_blank" style="color:#007bff;text-decoration:underline;font-weight:500;font-size:1.05rem;">BOM文件</a>
        </div>
        <h1 style="flex:1;text-align:center;margin:0;">产品库存监控系统</h1>
        <div class="sync-info" style="margin-left:auto;">
            <span class="sync-time">最后同步: {{ syncTime }}</span>
            <button class="refresh-btn" @click="refreshData">
                <span class="refresh-icon">↻</span>
            </button>
        </div>
    </div>
    <div class="content">
        <div class="panel">
            <div class="panel-header">
                <span>产品分类</span>
            </div>
            <div class="panel-content">
                <div
                        v-for="category in categories"
                        :key="category.id"
                        class="category-item"
                        :class="{
                            'active-category': selectedCategory && selectedCategory.id === category.id,
                            'editable': !category.fixed,
                            'editing': editingCategoryId === category.id
                        }"
                        :style="{ backgroundColor: category.color }"
                        @click="selectCategory(category)"
                        @dblclick="startEditCategory(category)"
                        :draggable="!category.fixed"
                        @dragstart="handleCategoryDragStart($event, category)"
                        @dragover="handleCategoryDragOver($event)"
                        @drop="handleCategoryDrop($event, category)"
                >
                    <template v-if="editingCategoryId === category.id">
                        <input
                            type="text"
                            v-model="editingCategoryName"
                            @click.stop
                            @keyup.enter="saveEditCategory"
                            @keyup.esc="cancelEditCategory"
                            ref="categoryNameInput"
                            class="category-edit-input"
                            v-focus
                        >
                        <input
                            type="color"
                            v-model="editingCategoryColor"
                            @click.stop
                            class="category-edit-color"
                        >
                        <div class="category-edit-actions">
                            <button class="category-edit-btn save" @click.stop="saveEditCategory">✓</button>
                            <button class="category-edit-btn cancel" @click.stop="cancelEditCategory">✕</button>
                        </div>
                    </template>
                    <template v-else>
                        <div class="category-content">
                            <span class="category-name" :title="category.name">{{ category.name }}</span>
                            <button class="delete-category-btn" @click.stop="deleteCategory(category)">×</button>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <div class="panel">
            <div class="panel-header">
                <span>产品列表</span>
            </div>
            <div class="panel-content" ref="scrollContainer">
                <div class="product-grid" ref="scrollContent">
                    <div
                            v-for="product in products"
                            :key="product.id"
                            class="product-card"
                            :class="{ 'low-stock': product.stock < product.threshold, 'disabled': product.disabled }"
                            :style="{ borderLeft: '5px solid ' + (product.category.color || '#ccc') }"
                            :draggable="selectedCategory && selectedCategory.id !== 0"
                            @dragstart="handleDragStart($event, product)"
                            @dragover="handleDragOver($event)"
                            @drop="handleDrop($event, product)"
                    >
                        <div class="product-order-number">{{ product.orderNo }}</div>
                        <div class="product-card-actions">
                            <button class="settings-btn" @click.stop="openSettings(product)">⚙</button>
                            <button class="delete-btn" @click.stop="deleteProduct(product)">×</button>
                        </div>
                        <div class="product-short-name">{{ product.shortName }}</div>
                        <h3 :title="product.name" :class="{ 'marquee': product.name.length > 15 }">{{ product.name }}</h3>
                        <div class="product-stock" :class="getStockClass(product)">
                            {{ product.stockStr ?? '--' }}
                        </div>
                        <div class="factory-stocks">
                            <div class="factory-stock factory-1" :title="'加工厂1库存: ' + (product.factory1Stock || '--')">{{ product.factory1Stock || '--' }}</div>
                            <div class="factory-stock factory-2" :title="'加工厂2库存: ' + (product.factory2Stock || '--')">{{ product.factory2Stock || '--' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="floating-menu" v-show="showFloatingMenu">
        <div class="menu-item" @click="showAddCategoryModal = true; showFloatingMenu = false">添加分类</div>
        <div class="menu-item" @click="showAddProductModal = true; showFloatingMenu = false">添加产品</div>
    </div>
    <button class="floating-btn" @click="toggleFloatingMenu">+</button>

    <div class="modal-overlay" v-show="showAddCategoryModal">
        <div class="add-category-modal">
            <div class="modal-header">添加新分类</div>
            <div class="modal-body">
                <input type="text" v-model="newCategoryName" placeholder="输入分类名称">
                <input type="color" v-model="newCategoryColor" style="height: 40px;">
            </div>
            <div class="modal-footer">
                <button class="modal-btn cancel" @click="showAddCategoryModal = false">取消</button>
                <button class="modal-btn confirm" @click="addCategory">确认</button>
            </div>
        </div>
    </div>

    <div class="modal-overlay" v-show="showAddProductModal">
        <div class="add-product-modal">
            <div class="modal-header">{{ editingProduct ? '编辑产品' : '添加新产品' }}</div>
            <div class="modal-body">
                <input type="text" v-model="newProductCode" placeholder="输入产品编号" required maxlength="255">
                <select v-model="selectedCategoryForProduct">
                    <option v-for="category in categories" :value="category" :key="category.id" v-if="category.id > 0">
                        {{ category.name }}
                    </option>
                </select>
                <input type="text" v-model="newProductShortName" placeholder="输入产品简称" required maxlength="16">
                <input type="number" v-model.number="newProductThreshold" placeholder="输入库存阈值" required min="0" max="99999999">
                <input type="number" v-model.number="newFactory1Stock" placeholder="加工厂1库存，非必填" min="0" max="99999999">
                <input type="number" v-model.number="newFactory2Stock" placeholder="加工厂2库存，非必填" min="0" max="99999999">
            </div>
            <div class="modal-footer">
                <button class="modal-btn cancel" @click="hideAddClearProductModal">取消</button>
                <button class="modal-btn confirm" @click="addProduct">确认</button>
            </div>
        </div>
    </div>
</div>

<script src="js/libs/vue.min.js"></script>
<script src="js/libs/vue-toast-notification/index.min.js"></script>
<script src="js/libs/axios.js"></script>
<script src="js/index.js"></script>
</body>
</html>

/* 基础样式 - 继承主页面风格 */
body {
    font-family: 'Orbitron', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #0f1923;
    color: #fff;
    overflow: hidden;
}

#global-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 25, 35, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(0, 150, 255, 0.3);
    border-radius: 50%;
    border-top-color: #00ffff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background: linear-gradient(90deg, #1a2a3a, #0f1923);
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header h1 {
    margin: 0;
    font-size: 2.5rem;
    letter-spacing: 2px;
    text-shadow: 0 0 10px #00ffff, 0 0 20px #0088ff;
}

.content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    height: calc(100vh - 100px);
    padding: 20px;
}

.panel {
    background: rgba(15, 25, 35, 0.7);
    border-radius: 5px;
    border: 1px solid #1a3a5a;
    box-shadow: 0 0 15px rgba(0, 150, 255, 0.3);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.panel-header {
    padding: 15px 20px;
    background: rgba(10, 20, 30, 0.8);
    border-bottom: 1px solid #1a3a5a;
    font-size: 1.2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #00ffff;
    text-shadow: 0 0 5px #0088ff;
}

.panel-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* 文件上传区域样式 */
.upload-area {
    border: 2px dashed #1a3a5a;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.02);
}

.upload-area:hover {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.05);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
}

.upload-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.7;
}

.upload-text p {
    margin: 10px 0;
    font-size: 1.1rem;
}

.upload-hint {
    color: #888;
    font-size: 0.9rem !important;
}

/* 上传进度样式 */
.upload-progress {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ffff, #0088ff);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    text-align: center;
    color: #00ffff;
    font-size: 0.9rem;
}

/* 文件列表样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.file-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.file-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: #00ffff;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.2);
}

.file-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.file-icon {
    font-size: 2rem;
    margin-right: 15px;
    opacity: 0.8;
}

.file-details {
    flex: 1;
}

.file-name {
    font-size: 1rem;
    color: #fff;
    margin-bottom: 4px;
    word-break: break-all;
}

.file-meta {
    font-size: 0.8rem;
    color: #888;
}

.file-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.3s ease;
}

.download-btn {
    background: rgba(0, 255, 255, 0.2);
    color: #00ffff;
}

.download-btn:hover {
    background: rgba(0, 255, 255, 0.4);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.delete-btn {
    background: rgba(255, 100, 100, 0.2);
    color: #ff6666;
}

.delete-btn:hover {
    background: rgba(255, 100, 100, 0.4);
    box-shadow: 0 0 10px rgba(255, 100, 100, 0.5);
}

/* 刷新按钮样式 */
.refresh-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.refresh-icon {
    font-size: 1.2rem;
    color: #00ffff;
    text-shadow: 0 0 5px #0088ff;
    display: inline-block;
    transition: transform 0.3s ease;
}

.refresh-btn:hover {
    background: rgba(0, 150, 255, 0.2);
}

.refresh-btn:hover .refresh-icon {
    transform: rotate(180deg);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .content {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }

    .header h1 {
        font-size: 1.8rem;
    }

    .upload-area {
        padding: 30px 15px;
    }

    .upload-icon {
        font-size: 3rem;
    }
}

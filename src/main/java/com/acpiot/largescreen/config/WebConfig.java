package com.acpiot.largescreen.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.UrlResource;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import pers.mx.fileserver.FileService;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private final FileService fileService;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        Path download = fileService.resolvePath("./download");
        try {
            Files.createDirectories(download);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        try {
            registry.addResourceHandler("/download/**")
                    .addResourceLocations(new UrlResource(download.toUri()))
                    .setCachePeriod(0);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
    }

}

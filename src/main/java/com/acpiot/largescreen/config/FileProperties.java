package com.acpiot.largescreen.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.io.Resource;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "file")
@Data
public class FileProperties {

    public enum FileType {
        DOWNLOAD
    }

    private final Map<FileType, Resource> resources = new HashMap<>();

    public Resource getResource(FileType type) {
        return resources.get(type);
    }

}

package com.acpiot.largescreen.mvc.controller;

import com.acpiot.largescreen.mvc.facade.MaterialStockCheckFacade;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/material")
public class MaterialStockCheckController {

    private final MaterialStockCheckFacade materialStockCheckFacade;

    @GetMapping("list")
    public String[] list() {
        return materialStockCheckFacade.listFileNames();
    }

    @PostMapping("upload")
    public void uploadFile(@RequestParam("file") MultipartFile file) throws IOException {
        materialStockCheckFacade.checkMaterialStock(file);
    }

    @DeleteMapping("delete/{fileName}")
    public void deleteFile(@PathVariable("fileName") String fileName) throws IOException {
        materialStockCheckFacade.deleteFile(fileName);
    }
}

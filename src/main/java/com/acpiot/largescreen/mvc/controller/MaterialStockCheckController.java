package com.acpiot.largescreen.mvc.controller;

import com.acpiot.largescreen.mvc.facade.MaterialStockCheckFacade;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/material")
public class MaterialStockCheckController {

    private final MaterialStockCheckFacade materialStockCheckFacade;

    @PostMapping("upload")
    public void uploadFile(@RequestParam("file") MultipartFile file) throws IOException {
        materialStockCheckFacade.checkMaterialStock(file);
    }
}
